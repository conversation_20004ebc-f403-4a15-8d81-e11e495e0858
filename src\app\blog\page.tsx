'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { BlogHero } from '@/components/blog/BlogHero';
import { BlogCard } from '@/components/blog/BlogCard';
import { FeaturedTools } from '@/components/blog/FeaturedTools';
import { TrendingTopics } from '@/components/blog/TrendingTopics';
import { AboutPlatform } from '@/components/blog/AboutPlatform';
import { fetchBlogPosts } from '@/services/blogService';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface BlogPost {
  id: string;
  _id: string;
  title: string;
  excerpt: string;
  content: string;
  slug: string;
  featuredImage?: string;
  imageCredit?: string;
  categories: string[];
  tags: string[];
  publishedAt: string;
  author: {
    name: string;
    email: string;
  };
}

export default function BlogPage() {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const postsPerPage = 9; // 3x3 grid

  useEffect(() => {
    loadPosts();
  }, [currentPage]);

  const loadPosts = async () => {
    try {
      setLoading(true);
      const response = await fetchBlogPosts({
        page: currentPage,
        limit: postsPerPage,
        status: 'published'
      });

      if (response.success && response.data) {
        setPosts(response.data);
        if (response.pagination) {
          setTotalPages(response.pagination.totalPages);
        }
      }
    } catch (error) {
      console.error('Error loading posts:', error);
      // Fallback to mock data if API fails
      const mockPosts = generateMockPosts();
      setPosts(mockPosts);
      setTotalPages(Math.ceil(mockPosts.length / postsPerPage));
    } finally {
      setLoading(false);
    }
  };

  const generateMockPosts = (): BlogPost[] => {
    return [
      {
        id: "1",
        _id: "1",
        title: "What's New In 2024 Tech",
        excerpt: "Discover the latest technological innovations and trends that are shaping the digital landscape in 2024.",
        content: "Technology continues to evolve...",
        slug: "whats-new-2024-tech",
        featuredImage: "https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800&q=80",
        imageCredit: "Unsplash",
        categories: ["Technology"],
        tags: ["Tech", "Innovation", "2024"],
        publishedAt: "2024-01-15",
        author: { name: "Jane Doe", email: "<EMAIL>" }
      },
      {
        id: "2",
        _id: "2",
        title: "Delicious Food Trends",
        excerpt: "Explore the culinary innovations and food trends that are taking the world by storm this year.",
        content: "Food culture is constantly evolving...",
        slug: "delicious-food-trends",
        featuredImage: "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800&q=80",
        imageCredit: "Unsplash",
        categories: ["Food"],
        tags: ["Food", "Trends", "Culinary"],
        publishedAt: "2024-01-10",
        author: { name: "John Doe", email: "<EMAIL>" }
      },
      {
        id: "3",
        _id: "3",
        title: "Race To Your Heart Content",
        excerpt: "Discover how automotive technology is revolutionizing the way we think about transportation and mobility.",
        content: "The automotive industry is experiencing...",
        slug: "race-to-your-heart-content",
        featuredImage: "https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&q=80",
        imageCredit: "Unsplash",
        categories: ["Automotive"],
        tags: ["Cars", "Technology", "Innovation"],
        publishedAt: "2024-01-05",
        author: { name: "John Doe", email: "<EMAIL>" }
      },
      // Add more mock posts to fill the grid
      ...Array.from({ length: 6 }, (_, i) => ({
        id: `${i + 4}`,
        _id: `${i + 4}`,
        title: `Amazing Article ${i + 4}`,
        excerpt: `This is an amazing article about various topics that will interest you and provide valuable insights.`,
        content: "Content here...",
        slug: `amazing-article-${i + 4}`,
        featuredImage: `https://images.unsplash.com/photo-${1500000000000 + i}?w=800&q=80`,
        imageCredit: "Unsplash",
        categories: ["General"],
        tags: ["Article", "Content"],
        publishedAt: `2024-01-${String(i + 1).padStart(2, '0')}`,
        author: { name: "Author Name", email: "<EMAIL>" }
      }))
    ];
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />

      {/* Hero Section */}
      <BlogHero />

      {/* Blog Cards Grid Section */}
      <section className="py-16 bg-background">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Latest Articles</h2>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              Discover insights, tutorials, and tips to enhance your productivity
            </p>
          </motion.div>

          {loading ? (
            <div className="text-center py-12">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent"></div>
              <p className="mt-4 text-muted-foreground">Loading articles...</p>
            </div>
          ) : (
            <>
              {/* Blog Cards Grid */}
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12"
              >
                {posts.map((post, index) => (
                  <motion.div key={post.id} variants={itemVariants}>
                    <BlogCard post={post} index={index} />
                  </motion.div>
                ))}
              </motion.div>

              {/* Pagination */}
              {totalPages > 1 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  className="flex justify-center items-center gap-4"
                >
                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className="flex items-center gap-2"
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Previous
                  </Button>

                  <div className="flex gap-2">
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                      <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        onClick={() => setCurrentPage(page)}
                        className="w-10 h-10"
                      >
                        {page}
                      </Button>
                    ))}
                  </div>

                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="flex items-center gap-2"
                  >
                    Next
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </motion.div>
              )}
            </>
          )}
        </div>
      </section>

      {/* Featured Tools Section */}
      <FeaturedTools />

      {/* Trending Topics Slider */}
      <TrendingTopics />

      {/* About Platform Section */}
      <AboutPlatform />

      <Footer />
    </div>
  );
}
